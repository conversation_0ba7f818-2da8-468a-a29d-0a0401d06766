<!doctype html>
<html>

<head></head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width,user-scalable=no,maximum-scale=1"/>
<link rel="shortcut icon" href="/favicon/favicon.ico"/>
<?php
wp_head();
global $title, $meta_keywords, $meta_description, $h1, $wp;

// Page meta data
$page_meta = [
    'home' => [
        'title' => "株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業"
    ],
    'about' => [
        'title' => "会社概要｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "会社概要｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業"
    ],
    'introduce' => [
        'title' => "メキシコ産牛肉って美味しいの？｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "管理体制｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業"
    ],
    'business' => [
        'title' => "事業内容｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "事業内容｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業"
    ],
    'privacy' => [
        'title' => "個人情報保護方針｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "個人情報保護方針｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業"
    ],
    'sitepolicy' => [
        'title' => "サイトポリシー｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "サイトポリシー｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業"
    ],
    'recruit' => [
        'title' => "採用情報｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "採用情報｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業"
    ],
    'inquiry' => [
        'title' => "お問い合わせ｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "お問い合わせ｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業",
        'scripts' => [get_template_directory_uri() . '/js/input_validation.js']
    ],
    'inquiry/confirm' => [
        'title' => "お問い合わせ｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "お問い合わせ｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業",
        'scripts' => [get_template_directory_uri() . '/js/input_validation.js']
    ],
    'inquiry/complete' => [
        'title' => "お問い合わせ｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "お問い合わせ｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業",
        'scripts' => [get_template_directory_uri() . '/js/input_validation.js']
    ],
    'jobs' => [
        'title' => "応募フォーム｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "応募フォーム｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業",
        'scripts' => [get_template_directory_uri() . '/js/job_validation.js']
    ],
    'jobs/confirm' => [
        'title' => "応募フォーム｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "応募フォーム｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業",
        'scripts' => [get_template_directory_uri() . '/js/job_validation.js']
    ],
    'jobs/complete' => [
        'title' => "応募フォーム｜株式会社アスモトレーディング｜食肉卸売事業、通信販売事業・食肉加工品・卸売事業",
        'description' => "応募フォーム｜株式会社アスモトレーディングは、食肉卸売事業、通信販売事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,通信販売事業",
        'scripts' => [get_template_directory_uri() . '/js/job_validation.js']
    ],
    'default' => [
        'title' => "404 Not Found｜株式会社アスモトレーディング",
        'description' => "株式会社アスモトレーディングは、食肉卸売事業、給食事業を通じて、安全で安心な「食」をお客様に提供してまいります。",
        'keywords' => "アスモトレーディング,食肉販売,食肉加工品,卸売事業,給食事業"
    ]
];

$current_page = is_home() || is_front_page() ? 'home' : ($wp->request ?? 'default');
$meta = $page_meta[$current_page] ?? $page_meta['default'];

$title = $meta['title'];
$meta_description = $meta['description'];
$meta_keywords = $meta['keywords'];

// Output page-specific scripts
if (!empty($meta['scripts'])) {
    foreach ($meta['scripts'] as $script) {
        echo '<script src="' . esc_url($script) . '" type="text/javascript"></script>';
    }
}
?>
<title><?php echo esc_html($title); ?></title>
<meta name="Keywords" content="<?php echo esc_attr($meta_keywords); ?>"/>
<meta name="Description" content="<?php echo esc_attr($meta_description); ?>"/>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick-theme.min.css"/>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.js"></script>
</head>

<body class="<?php echo (is_home() || is_front_page()) ? 'home' : ''; ?>">
<div>
    <header>
        <a href="/" id="logo">
            <img src="<?php echo esc_url(get_template_directory_uri() . '/images/renew/asmo_trading_logo.png'); ?>"
                 alt="Image 1">
        </a>
        <nav class="drawr">
            <ul>
                <li><a href="/">ホーム</a></li>
                <li><a href="/business">事業内容</a></li>
                <li><a href="/introduce">メキシコ産牛肉って美味しいの？</a></li>
                <li><a href="/about">会社概要</a></li>
                <li><a href="/recruit">採用情報</a></li>
                <li class="sp-only"><a href="/inquiry">お問い合わせ</a></li>
                <li class="sp-only"><a href="/sitepolicy">サイトポリシー</a></li>
                <li class="sp-only"><a href="/privacy">個人情報保護方針</a></li>
                <li class="copyright sp-only">Copyright© ASMOTRADING CORPORATION.<br/>All Rights Reserved.
                </li>
            </ul>
            <a class="contact-btn" href="/inquiry/">お問い合わせ</a>
            <div class="social-icons">
                <a href="https://www.instagram.com/asmo_trading/">
                    <img src="<?php echo esc_url(get_template_directory_uri() . '/images/renew/instagram_white.png'); ?>"
                         alt="Instagram">
                </a>
                <a href="#">
                    <img src="<?php echo esc_url(get_template_directory_uri() . '/images/renew/contact_white.png'); ?>"
                         alt="Contact">
                </a>
            </div>
        </nav>
        <a class="menu-btn"></a>
        <div id="bg_blur" class="sp-only"></div>
    </header>
    <div class="banner">
        <?php
        $bannerClassMap = [
            '/introduce' => 'custom-slider-banner',
            '/about' => 'custom-slider-banner-hidden',
            '/recruit' => 'banner-title-bg',
            '/business' => 'custom-slider-banner-hidden',
            '/inquiry' => 'custom-slider-banner-hidden',
            '/privacy' => 'custom-slider-banner-hidden',
            '/sitepolicy' => 'custom-slider-banner-hidden',
        ];
        $currentUrl = rtrim(strtok($_SERVER['REQUEST_URI'], '?'), '/');
        $bannerClass = $bannerClassMap[$currentUrl] ?? '';

        $classBannerUrl = [
            '/business'=> 'custom_banner',
            '/introduce' => 'custom_banner',
            '/about' => 'custom_banner',
            '/recruit' => 'custom_banner',
            '/inquiry' => 'custom_banner',
        ];
        ?>
        <?php if ($currentUrl !== '/recruit' && $currentUrl !== '/introduce'): ?>
         <div class="banner-title <?php echo esc_attr($bannerClass); ?>">
            <h2><span>食肉卸売事業、</span>通信販売事業を通じて、</h2>
            <h2>安全で安心な「食」をお客様に提供してまいります。</h2>
        </div>
        <?php endif; ?>
        <div class="slider-container <?php echo isset($classBannerUrl[$currentUrl]) ? esc_attr($classBannerUrl[$currentUrl]) : ''; ?>">
            <div class="header-slider">
                <!-- Trình chiếu slider page đầu -->
                <div class="slide home-slide"><img
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/groups/slide pictureü@ç@.png"
                            alt="Slide 1"/></div>
                <div class="slide home-slide"><img
                style=" object-position: center 0; "
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/groups/slide pictureü@çA.png"
                            alt="Slide 2"/></div>
                <div class="slide home-slide"><img
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/groups/slide pictureü@çB.png"
                            alt="Slide 3"/></div>
                <div class="slide home-slide"><img
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/renew/slide1.png"
                            alt="Slide 3"/></div>

                <!-- Hiển thị hình ảnh trình chiếu slider recruit -->
                <div class="slide inquiry-slide"><img
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/groups/recruit image.png"
                            alt="Slide 4"/></div>
                <div class="slide inquiry-slide"><img
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/groups/recruitç@.jpg"
                            alt="Slide 5"/></div>
                <div class="slide inquiry-slide"><img
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/groups/recruitçA.jpg"
                            alt="Slide 6"/></div>

                <!-- Hiển thị hình ảnh cố định theo url -->
                <div class="slide other-slide" data-url="/business"><img
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/groups/Business details.png"
                            alt="Slide 7"/></div>
                <div class="slide other-slide" data-url="/introduce"><img
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/groups/Mexico Meet.jpg"
                            alt="Slide 8"/></div>
                <div class="slide other-slide" data-url="/privacy"><img
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/groups/privacyüòcopyright.jpg"
                            alt="Slide 9"/></div>
                <div class="slide other-slide" data-url="/inquiry"><img
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/groups/contact.jpg"
                            alt="Slide 10"/></div>
                <div class="slide other-slide" data-url="/about"><img
                            src="<?php echo esc_url(get_template_directory_uri()); ?>/images/groups/company profile.jpg"
                            alt="Slide 11"/></div>
            </div>
        </div>
    </div>
    <div>
        <script>
            jQuery(function ($) {
                var currentUrl = window.location.pathname.replace(/\/$/, '');

                // Highlight active menu link
                $('nav ul li a').each(function () {
                    var href = $(this).attr('href').replace(/\/$/, '');
                    if (href === currentUrl) {
                        $(this).addClass('active');
                    }
                });

                var slideMap = {
                    '/': [0, 1, 2, 3],
                    '': [0, 1, 2, 3],
                    // '/recruit': [4, 5, 6],
                    // '/recruit': [4, 5, 6],
                    '/business': [7],
                    '/introduce': [8],
                    '/sitepolicy': [9],
                    '/inquiry': [10],
                    '/about': [11],
                    '/privacy': [9]
                };

                var indices = slideMap.hasOwnProperty(currentUrl) ? slideMap[currentUrl] : [0];

                // Keep only relevant slides
                $('.header-slider .slide').each(function (index) {
                    if (!indices.includes(index)) {
                        $(this).remove();
                    }
                });

                // Now only relevant slides are left, show the slider
                $('.header-slider').css('visibility', 'visible');

                // Initialize Slick after DOM is ready with correct slides
                $('.header-slider').slick({
                    autoplay: indices.length > 1,
                    autoplaySpeed: 3000,
                    speed: 5000,
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    arrows: false,
                    dots: true,
                    pauseOnHover: false,
                    pauseOnFocus: false,
                    fade: true
                });

                // Banner class map for styling
                var bannerClassMap = {
                    1: 'custom-slider-banner',
                    2: 'custom-slider-banner-hidden',
                    3: 'banner-title-bg'
                };

                $('.header-slider').on('beforeChange', function (event, slick, currentSlide, nextSlide) {
                    var $bannerTitle = $('.banner-title');
                    $bannerTitle.removeClass('custom-slider-banner custom-slider-banner-hidden banner-title-bg');
                    if (bannerClassMap[nextSlide]) {
                        $bannerTitle.addClass(bannerClassMap[nextSlide]);
                    }
                });
            });
        </script>

        <style>
            .custom-slider-banner {
                width: 100%;
                display: grid;
                justify-content: end;
            }

            .custom-slider-banner-hidden {
                display: none;
            }

            .banner-title-bg {
                background-color: #9aa7b447;
            }

            .header-slider {
                visibility: hidden;
            }

            /* .header-slider .slide img {
                 width: 100%;
                 height: auto;
                 display: block;
                 max-width: 100%;
                 object-fit: cover;
            } */

            @media screen and (max-width: 640px) {
                .header-slider .slide img {
                    width: 100%;
                    height: auto;
                    display: block;
                    max-width: 100%;
                    object-fit: cover;
                }

                .slick-dots {
                    bottom: 26rem !important;
                }

                .slider-container {
                    height: 18rem;
                }

                .slick-slider .slick-track,
                .slick-slider .slick-list {
                    position: relative;
                    /*top: 40px;*/
                    margin-bottom: 0;
                }

                .custom-slider-banner {
                    width: 100%;
                    display: table-caption;
                    justify-content: end;
                }
                .site-policy .title h3{
                        color: #212121;
                }
               /* Apply max-height only on homepage */
               body.home .slider-container {
                max-height: 44rem;
               }
            }
        </style>
    </div>
</div>
</body>

</html>